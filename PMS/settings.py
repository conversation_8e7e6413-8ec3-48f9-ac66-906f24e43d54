from pathlib import Path
from dotenv import load_dotenv
import os
import sys

# Load environment variables from a .env file
load_dotenv()

# Base directory of the project
BASE_DIR = Path(__file__).resolve().parent.parent

# Secret key for cryptographic operations
SECRET_KEY = os.environ.get('SECRET_KEY')
if not SECRET_KEY:
    sys.exit("Error: SECRET_KEY not set in environment variables.")

# Debug mode (ensure it's properly set to a boolean)
DEBUG = os.environ.get('DEBUG', 'False').lower() in ('true', '1', 'yes', 'on')

# Allowed hosts (read from environment variable and split by comma)
ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',')

# Installed applications
INSTALLED_APPS = [
    # Django apps
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # Custom apps
    'main',
]

# Middleware configuration
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',

    # Third-party middleware
    'whitenoise.middleware.WhiteNoiseMiddleware',
    # Custom middleware
    'main.backends.middleware.LoginCheckMiddleWare',
]

# Root URL configuration
ROOT_URLCONF = 'PMS.urls'

# Templates configuration
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

# WSGI application
WSGI_APPLICATION = 'PMS.wsgi.application'

# Database configuration
if DEBUG :
    # Use SQLite for development if MySQL is not configured
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }
else:
    # Use MySQL for production or when properly configured
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': os.environ.get('DB_NAME'),
            'USER': os.environ.get('DB_USER'),
            'PASSWORD': os.environ.get('DB_PASSWORD'),
            'HOST': os.environ.get('DB_HOST'),
            'PORT': os.environ.get('DB_PORT'),
            'OPTIONS': {
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'"
            }
        }
    }

# Ensure all required database environment variables are set (only in production)
if not DEBUG:
    required_db_vars = ['DB_NAME', 'DB_USER', 'DB_HOST', 'DB_PORT']
    missing_db_vars = [var for var in required_db_vars if not os.environ.get(var)]
    if missing_db_vars:
        sys.exit(f"Error: Missing database configuration variables: {', '.join(missing_db_vars)}")
else:
    # In development, provide helpful warnings for missing DB vars
    required_db_vars = ['DB_NAME', 'DB_USER', 'DB_HOST', 'DB_PORT']
    missing_db_vars = [var for var in required_db_vars if not os.environ.get(var)]
    if missing_db_vars:
        print(f"Warning: Missing database configuration variables: {', '.join(missing_db_vars)}")
        print("Please update your .env file with the correct database settings.")

# Password validation configuration
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Localization settings
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'Asia/Kolkata'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images) configuration
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Custom user model
AUTH_USER_MODEL = 'main.CustomUser'
AUTHENTICATION_BACKENDS = [
    'main.backends.EmailBackend.EmailBackend',
]

# WhiteNoise static files storage
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Email server configuration
EMAIL_HOST = os.environ.get('EMAIL_HOST')
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_ADDRESS')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_PASSWORD')

# Ensure all required email environment variables are set (only in production)
if not DEBUG:
    required_email_vars = ['EMAIL_HOST', 'EMAIL_ADDRESS', 'EMAIL_PASSWORD']
    missing_email_vars = [var for var in required_email_vars if not os.environ.get(var)]
    if missing_email_vars:
        sys.exit(f"Error: Missing email configuration variables: {', '.join(missing_email_vars)}")
else:
    # In development, use console backend if email vars are missing
    required_email_vars = ['EMAIL_HOST', 'EMAIL_ADDRESS', 'EMAIL_PASSWORD']
    missing_email_vars = [var for var in required_email_vars if not os.environ.get(var)]
    if missing_email_vars:
        print(f"Warning: Missing email configuration variables: {', '.join(missing_email_vars)}")
        print("Using console email backend for development.")
        EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Google reCAPTCHA configuration
GOOGLE_RECAPTCHA_SECRET_KEY = os.environ.get('GOOGLE_RECAPTCHA_SECRET_KEY')
GOOGLE_RECAPTCHA_SITE_KEY = os.environ.get('GOOGLE_RECAPTCHA_SITE_KEY')

# Ensure all required reCAPTCHA environment variables are set (only in production)
if not DEBUG:
    required_recaptcha_vars = ['GOOGLE_RECAPTCHA_SECRET_KEY', 'GOOGLE_RECAPTCHA_SITE_KEY']
    missing_recaptcha_vars = [var for var in required_recaptcha_vars if not os.environ.get(var)]
    if missing_recaptcha_vars:
        sys.exit(f"Error: Missing reCAPTCHA configuration variables: {', '.join(missing_recaptcha_vars)}")

# Development settings
if DEBUG:
    # Add django-debug-toolbar if available
    try:
        import debug_toolbar
        INSTALLED_APPS.append('debug_toolbar')
        MIDDLEWARE.insert(0, 'debug_toolbar.middleware.DebugToolbarMiddleware')
        INTERNAL_IPS = ['127.0.0.1', 'localhost']
    except ImportError:
        pass

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Session settings
SESSION_COOKIE_AGE = 86400  # 24 hours
SESSION_SAVE_EVERY_REQUEST = True
SESSION_EXPIRE_AT_BROWSER_CLOSE = True

# Security settings (adjust for production)
if not DEBUG:
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    X_FRAME_OPTIONS = 'DENY'
    SECURE_HSTS_SECONDS = 31536000
    SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    SECURE_HSTS_PRELOAD = True
