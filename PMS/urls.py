from django.contrib import admin
from django.urls import path, include
from django.conf.urls.static import static
from . import settings

# URL patterns for the application
urlpatterns = [
    path('', include('main.urls')),  # Include main app URLs
    path('accounts/', include("django.contrib.auth.urls")),  # Include authentication URLs
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT) + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)  # Serve static+media files in development

# Custom error handlers
handler404 = 'main.views.custom_404'
handler500 = 'main.views.custom_500'
