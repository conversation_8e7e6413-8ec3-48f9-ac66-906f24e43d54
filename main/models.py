from django.db import models
from django.contrib.auth.models import UserManager, AbstractUser
from django.core.validators import  validate_email
from django.core.exceptions import ValidationError
from django.contrib.auth.hashers import make_password
from django.db.models import CheckConstraint
from django.dispatch import receiver
from django.db.models.signals import post_save
from django.db import IntegrityError



class CustomUserManager(UserManager):
    def _create_user(self, email, password, **extra_fields):
        if not email:
            raise ValueError("The Email field must be set")
        try:
            validate_email(email)
        except ValidationError:
            raise ValueError("The Email field must be a valid email address")
        email = self.normalize_email(email)
        user = CustomUser(email=email, **extra_fields)
        user.password = make_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, email, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", False)
        extra_fields.setdefault("is_superuser", False)
        return self._create_user(email, password, **extra_fields)

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        assert extra_fields["is_staff"]
        assert extra_fields["is_superuser"]
        return self._create_user(email, password, **extra_fields)

class CustomUser(AbstractUser):
    USER_TYPE_CHOICES = (
        (1, "Admin"),
        (2, "Accountant"),
        (3, "Staff"),
    )
    GENDER_CHOICES = [("M", "Male"), ("F", "Female"), ('O', 'Other')]

    username = None
    email = models.EmailField(unique=True, validators=[validate_email]) 
    user_type = models.PositiveSmallIntegerField(choices=USER_TYPE_CHOICES, default=1)
    gender = models.CharField( choices=GENDER_CHOICES,max_length=1, default='M' )  
    profile_pic = models.ImageField(null=True, blank=True)
    father_name = models.CharField(max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    objects = CustomUserManager()

    def __str__(self):
        return self.email

class Admin(models.Model):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    
    def __str__(self):
        return f"Admin: {self.user.email}"  

class Accountant(models.Model):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, null=True, blank=True)

    def __str__(self):
        return f"Accountant: {self.user.email}" 
 
class Division(models.Model):
    name = models.CharField(max_length=50, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True
    def __str__(self):
        return self.name

class Department(models.Model):
    code = models.CharField(max_length=10, unique=True, db_index=True)
    name = models.CharField(max_length=50)
    division = models.ForeignKey('Division', on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True

    def __str__(self):
        return f"{self.code} - {self.name}"

    class Meta:
        managed = True
    def __str__(self):
        return self.name

class Designation(models.Model):
    name = models.CharField(max_length=30)
    department = models.ForeignKey('Department',on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True
        unique_together = ('department', 'name')
    def __str__(self):
        return self.name

class Grade(models.Model):
    name = models.CharField(max_length=100)
    start = models.PositiveIntegerField()  
    end = models.PositiveIntegerField()    
    increment = models.PositiveIntegerField()
    medical = models.PositiveIntegerField(default=0)
    adhoc = models.PositiveIntegerField()
    conva = models.PositiveIntegerField()
    cca = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            CheckConstraint(check=models.Q(start__lte=models.F('end')), name='start_lte_end')
        ]

    def clean(self):
        if self.start > self.end:
            raise ValidationError('Start value must be less than or equal to end value.')
        if self.increment <= 0:
            raise ValidationError('Increment value must be greater than zero.')
    
    def save(self, *args, **kwargs):
        self.full_clean()  
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name
  
class Staff(models.Model):
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    division = models.ForeignKey(Division, on_delete=models.DO_NOTHING, null=True)
    designation = models.ForeignKey(Designation, on_delete=models.DO_NOTHING, null=True)
    department = models.ForeignKey(Department, on_delete=models.DO_NOTHING, null=True)
    emp_code = models.CharField(max_length=20, unique=True)
    uan = models.CharField(max_length=20, null=True)
    emp_doj = models.DateField(null=True)
    grade = models.ForeignKey(Grade, on_delete=models.DO_NOTHING, null=True)
    basic_amt = models.PositiveIntegerField(null=True)
    employment_type = models.CharField(max_length=20, choices=(('Regular', 'Regular'), ('Contract', 'Contract'), ('Active', 'Active')), default='Regular')
    is_active = models.BooleanField(default=True)
    cca = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f'Staff: {self.user.email}'

class Fixed(models.Model):
    division = models.ForeignKey('Division', on_delete=models.CASCADE, null=True)
    month = models.DateField(null=True)
    da = models.FloatField(null=True)
    hra = models.FloatField(null=True)

    def __str__(self):
        return f"Fixed Value for {self.month}"

class Deductions(models.Model):
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE)
    income_tax = models.PositiveIntegerField(null=True)
    canteen = models.PositiveIntegerField(null=True)
    advance = models.PositiveIntegerField(null=True)
    society = models.PositiveIntegerField(null=True)
    insurance = models.PositiveIntegerField(null=True)
    other = models.PositiveIntegerField(null=True)  

    def __str__(self):
        return f"Deductions for {self.staff}"

class ContractPay(models.Model):
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE)
    month = models.DateField(null=True)
    adhoc = models.PositiveIntegerField(null=True)
    hra = models.PositiveIntegerField(null=True)
    arrears = models.PositiveIntegerField(null=True)
    other =  models.PositiveIntegerField(null=True)

    def __str__(self):
        return f"Contract Pay for {self.staff}"
    
class RegularPay(models.Model):
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE)
    arrears = models.PositiveIntegerField(null=True)
    other =  models.PositiveIntegerField(null=True)

    def __str__(self):
        return f"Regular Pay for {self.staff}"


class Attendance(models.Model):
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE)
    paid_days = models.FloatField(null=True)
    lop = models.FloatField(null=True)

    def __str__(self):
        return f"Attendance for {self.staff.user}"

class Payslip(models.Model):
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE)
    month = models.DateField()
    basic = models.PositiveIntegerField(default=0)
    da = models.PositiveIntegerField(default=0)
    dp = models.PositiveIntegerField(default=0)
    society = models.PositiveIntegerField(default=0)
    hra = models.PositiveIntegerField(default=0)
    conv = models.PositiveIntegerField(default=0)
    medical = models.PositiveIntegerField(default=0)
    cca = models.PositiveIntegerField(default=0)
    adhoc = models.PositiveIntegerField(default=0)
    Dother = models.PositiveIntegerField(default=0)
    Pother = models.PositiveIntegerField(default=0)
    arrears = models.PositiveIntegerField(default=0)
    gross_pay = models.PositiveIntegerField(default=0)
    epf = models.PositiveIntegerField(default=0)
    esi = models.PositiveIntegerField(default=0)
    income_tax = models.PositiveIntegerField(default=0)
    canteen = models.PositiveIntegerField(default=0)
    advance = models.PositiveIntegerField(default=0)
    insurance = models.PositiveIntegerField(default=0)
    total_deductions = models.PositiveIntegerField(default=0)
    net_pay = models.PositiveIntegerField(default=0)
    paid_days = models.PositiveIntegerField(default=0)
    lop = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('staff', 'month')

    def __str__(self):
        return f"Payslip for {self.staff} - {self.month:%B %Y}"


@receiver(post_save, sender=CustomUser)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        try:
            if instance.user_type == 1:
                Admin.objects.create(user=instance)
            elif instance.user_type == 2:
                Accountant.objects.create(user=instance)
            elif instance.user_type == 3:
                Staff.objects.create(user=instance)
        except IntegrityError as e:
            print(f"Error creating user profile: {e}")
            
        except Exception as e:  
            print(f"Unexpected error creating user profile: {e}")

@receiver(post_save, sender=CustomUser)
def save_user_profile(sender, instance, **kwargs):
    try:
        if instance.user_type == 1:
            instance.admin.save()
        elif instance.user_type == 2:
           instance.accountant.save()
        elif instance.user_type == 3:
            instance.staff.save()
        else:
            return
        
    except IntegrityError as e:
        print(f"IntegrityError while saving user profile for {instance.email}: {e}")
    except Exception as e: 
        print(f"Unexpected error while saving user profile for {instance.email}: {e}")
