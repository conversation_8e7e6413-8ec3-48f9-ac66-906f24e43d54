# Core Django Framework
Django>=4.2,<5.0

# Environment Variables
python-dotenv>=1.0.0

# Static Files Handling
whitenoise>=6.0.0

# Database (MySQL - optional, SQLite is used by default in development)
# mysqlclient>=2.1.0

# Additional useful packages for development
pillow>=10.0.0
num2words>=0.5.12
weasyprint>=60.0
requests>=2.31.0
xlsxwriter>=3.1.0

# Development tools (optional)
# django-debug-toolbar>=4.0.0
# django-extensions>=3.2.0

# Security
# django-cors-headers>=4.0.0

# Testing
# pytest-django>=4.5.0
# coverage>=7.0.0
